# تعليمات تشغيل موقع التبرعات لغزة

## 🚀 خطوات التشغيل السريع

### 1. إعداد XAMPP
1. تشغيل XAMPP Control Panel
2. تشغيل Apache و MySQL
3. التأكد من أن Apache يعمل على المنفذ 80

### 2. إعداد قاعدة البيانات
1. افتح المتصفح واذهب إلى: `http://localhost/phpmyadmin`
2. انقر على "New" لإنشاء قاعدة بيانات جديدة
3. اكتب اسم قاعدة البيانات: `gaza_donations`
4. اختر Collation: `utf8mb4_unicode_ci`
5. انقر على "Create"
6. انقر على قاعدة البيانات الجديدة
7. ان<PERSON>ر على تبويب "Import"
8. اختر الملف `database/gaza_donations.sql`
9. ان<PERSON><PERSON> على "Go"

### 3. تشغيل الواجهة الأمامية
```bash
# تثبيت المكتبات
npm install

# تشغيل الخادم المحلي
npm run dev
```

### 4. الوصول للموقع
- الموقع الرئيسي: `http://localhost:5173`
- لوحة الإدارة: `http://localhost:5173/admin`

## 🔐 بيانات الدخول للإدارة
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `17417723`

## 📁 هيكل الملفات المهمة

```
gaza/
├── api/
│   ├── config/
│   │   └── database.php          # إعدادات قاعدة البيانات
│   └── projects.php              # API للمشاريع
├── database/
│   └── gaza_donations.sql        # ملف قاعدة البيانات
├── src/
│   ├── components/
│   │   ├── admin/
│   │   │   ├── AdminDashboard.tsx # لوحة الإدارة الرئيسية
│   │   │   ├── AdminProjects.tsx  # إدارة المشاريع
│   │   │   └── AddProject.tsx     # إضافة مشروع جديد
│   │   └── Projects.tsx           # عرض المشاريع للزوار
│   └── ...
└── README_DATABASE.md             # تفاصيل قاعدة البيانات
```

## ✨ الميزات المتاحة

### للزوار:
- ✅ عرض المشاريع النشطة من قاعدة البيانات
- ✅ شريط التقدم الديناميكي
- ✅ تصنيف المشاريع
- ✅ واجهة عربية متجاوبة

### للإدارة:
- ✅ حماية بكلمة مرور
- ✅ عرض جميع المشاريع
- ✅ إضافة مشاريع جديدة
- ✅ حذف المشاريع
- ✅ البحث والفلترة
- ✅ إحصائيات المشاريع

## 🔧 استكشاف الأخطاء

### مشكلة: "خطأ في الاتصال بقاعدة البيانات"
**الحل:**
1. تأكد من تشغيل MySQL في XAMPP
2. تحقق من اسم قاعدة البيانات في `api/config/database.php`
3. تأكد من صحة بيانات الاتصال (username: root, password: فارغ)

### مشكلة: "CORS Error"
**الحل:**
1. تأكد من تشغيل Apache على المنفذ 80
2. تحقق من إعدادات CORS في ملفات PHP
3. تأكد من الرابط الصحيح: `http://localhost/gaza/api/projects.php`

### مشكلة: "البيانات لا تظهر"
**الحل:**
1. تحقق من استيراد ملف SQL بنجاح
2. افتح phpMyAdmin وتأكد من وجود البيانات
3. تحقق من console المتصفح للأخطاء

## 📊 البيانات التجريبية

تم إضافة البيانات التالية تلقائياً:
- **5 مشاريع** بحالات مختلفة (نشط، متوقف، مكتمل)
- **6 تبرعات** تجريبية
- **إحصائيات** محدثة تلقائياً

## 🔄 التحديثات التلقائية

- **المبلغ المجمع**: يتم تحديثه تلقائياً عند إضافة تبرع
- **حالة المشروع**: تتغير إلى "مكتمل" عند الوصول للهدف
- **النسبة المئوية**: محسوبة تلقائياً

## 📞 الدعم

في حالة وجود مشاكل:
1. تحقق من تشغيل XAMPP بشكل صحيح
2. تأكد من استيراد قاعدة البيانات
3. راجع console المتصفح للأخطاء
4. تحقق من ملفات PHP في مجلد `api/`
