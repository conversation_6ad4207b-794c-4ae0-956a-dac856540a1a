<?php
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

// إنشاء الجداول إذا لم تكن موجودة
$database->createTables();

$method = $_SERVER['REQUEST_METHOD'];

switch($method) {
    case 'GET':
        getProjects($db);
        break;
    case 'POST':
        createProject($db);
        break;
    case 'PUT':
        updateProject($db);
        break;
    case 'DELETE':
        deleteProject($db);
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'طريقة غير مدعومة']);
        break;
}

function getProjects($db) {
    try {
        $query = "SELECT 
            id,
            title,
            description,
            category,
            target_amount,
            raised_amount,
            status,
            image_url,
            created_at,
            updated_at,
            ROUND((raised_amount / target_amount) * 100, 2) as percentage
        FROM projects 
        ORDER BY created_at DESC";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => $projects
        ]);
    } catch(PDOException $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في جلب المشاريع: ' . $exception->getMessage()
        ]);
    }
}

// دالة توليد اسم عشوائي للصورة
function generateRandomImageName($originalName) {
    $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
    $randomName = 'project_' . uniqid() . '_' . time() . '.' . $extension;
    return $randomName;
}

// دالة رفع صورة المشروع
function uploadProjectImage($file) {
    $uploadDir = '../uploads/project_images/';

    // التحقق من وجود المجلد وإنشاؤه إذا لم يكن موجوداً
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // التحقق من نوع الملف
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    $fileType = $file['type'];

    if (!in_array($fileType, $allowedTypes)) {
        throw new Exception('نوع الملف غير مدعوم. يُسمح بالصور فقط (JPG, PNG, GIF, WebP)');
    }

    // التحقق من حجم الملف (10MB كحد أقصى)
    if ($file['size'] > 10 * 1024 * 1024) {
        throw new Exception('حجم الصورة كبير جداً. الحد الأقصى 10MB');
    }

    // التحقق من أن الملف صورة فعلاً
    $imageInfo = getimagesize($file['tmp_name']);
    if ($imageInfo === false) {
        throw new Exception('الملف المرفوع ليس صورة صالحة');
    }

    // توليد اسم عشوائي ورفع الملف
    $fileName = generateRandomImageName($file['name']);
    $filePath = $uploadDir . $fileName;

    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        return 'uploads/project_images/' . $fileName;
    } else {
        throw new Exception('فشل في رفع الصورة');
    }
}

function createProject($db) {
    try {
        // التحقق من البيانات المطلوبة
        if (!isset($_POST['title']) || !isset($_POST['category']) || !isset($_POST['target_amount'])) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'بيانات غير مكتملة'
            ]);
            return;
        }

        $image_url = null;

        // رفع الصورة إذا كانت موجودة
        if (isset($_FILES['project_image']) && $_FILES['project_image']['error'] === UPLOAD_ERR_OK) {
            $image_url = uploadProjectImage($_FILES['project_image']);
        }

        $query = "INSERT INTO projects (title, description, category, target_amount, image_url)
                  VALUES (:title, :description, :category, :target_amount, :image_url)";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':title', $_POST['title']);
        $stmt->bindParam(':description', $_POST['description']);
        $stmt->bindParam(':category', $_POST['category']);
        $stmt->bindParam(':target_amount', $_POST['target_amount']);
        $stmt->bindParam(':image_url', $image_url);

        if ($stmt->execute()) {
            $project_id = $db->lastInsertId();

            echo json_encode([
                'success' => true,
                'message' => 'تم إنشاء المشروع بنجاح',
                'project_id' => $project_id,
                'image_url' => $image_url
            ]);
        } else {
            throw new Exception('فشل في إنشاء المشروع');
        }
    } catch(Exception $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => $exception->getMessage()
        ]);
    }
}

function updateProject($db) {
    try {
        // التحقق من معرف المشروع
        if (!isset($_POST['id'])) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'معرف المشروع مطلوب'
            ]);
            return;
        }

        $project_id = $_POST['id'];
        $image_url = $_POST['current_image_url'] ?? null; // الاحتفاظ بالصورة الحالية

        // رفع صورة جديدة إذا كانت موجودة
        if (isset($_FILES['project_image']) && $_FILES['project_image']['error'] === UPLOAD_ERR_OK) {
            // حذف الصورة القديمة إذا كانت موجودة
            if ($image_url && file_exists('../' . $image_url)) {
                unlink('../' . $image_url);
            }

            $image_url = uploadProjectImage($_FILES['project_image']);
        }

        $query = "UPDATE projects SET
                  title = :title,
                  description = :description,
                  category = :category,
                  target_amount = :target_amount,
                  status = :status,
                  image_url = :image_url
                  WHERE id = :id";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $project_id);
        $stmt->bindParam(':title', $_POST['title']);
        $stmt->bindParam(':description', $_POST['description']);
        $stmt->bindParam(':category', $_POST['category']);
        $stmt->bindParam(':target_amount', $_POST['target_amount']);
        $stmt->bindParam(':status', $_POST['status'] ?? 'active');
        $stmt->bindParam(':image_url', $image_url);

        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث المشروع بنجاح',
                'image_url' => $image_url
            ]);
        } else {
            throw new Exception('فشل في تحديث المشروع');
        }
    } catch(Exception $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => $exception->getMessage()
        ]);
    }
}

function deleteProject($db) {
    $id = $_GET['id'] ?? null;

    if (!$id) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'معرف المشروع مطلوب'
        ]);
        return;
    }

    try {
        // جلب معلومات المشروع أولاً لحذف الصورة
        $query = "SELECT image_url FROM projects WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        $project = $stmt->fetch(PDO::FETCH_ASSOC);

        // حذف المشروع من قاعدة البيانات
        $query = "DELETE FROM projects WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id);

        if ($stmt->execute()) {
            // حذف الصورة من الخادم إذا كانت موجودة
            if ($project && $project['image_url'] && file_exists('../' . $project['image_url'])) {
                unlink('../' . $project['image_url']);
            }

            echo json_encode([
                'success' => true,
                'message' => 'تم حذف المشروع بنجاح'
            ]);
        } else {
            throw new Exception('فشل في حذف المشروع');
        }
    } catch(Exception $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في حذف المشروع: ' . $exception->getMessage()
        ]);
    }
}
?>
