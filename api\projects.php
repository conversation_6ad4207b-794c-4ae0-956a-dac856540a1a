<?php
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

// إنشاء الجداول إذا لم تكن موجودة
$database->createTables();

$method = $_SERVER['REQUEST_METHOD'];

switch($method) {
    case 'GET':
        getProjects($db);
        break;
    case 'POST':
        createProject($db);
        break;
    case 'PUT':
        updateProject($db);
        break;
    case 'DELETE':
        deleteProject($db);
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'طريقة غير مدعومة']);
        break;
}

function getProjects($db) {
    try {
        $query = "SELECT 
            id,
            title,
            description,
            category,
            target_amount,
            raised_amount,
            status,
            image_url,
            created_at,
            updated_at,
            ROUND((raised_amount / target_amount) * 100, 2) as percentage
        FROM projects 
        ORDER BY created_at DESC";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => $projects
        ]);
    } catch(PDOException $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في جلب المشاريع: ' . $exception->getMessage()
        ]);
    }
}

function createProject($db) {
    $data = json_decode(file_get_contents("php://input"), true);
    
    if (!$data || !isset($data['title']) || !isset($data['category']) || !isset($data['target_amount'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'بيانات غير مكتملة'
        ]);
        return;
    }
    
    try {
        $query = "INSERT INTO projects (title, description, category, target_amount, image_url) 
                  VALUES (:title, :description, :category, :target_amount, :image_url)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':title', $data['title']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':category', $data['category']);
        $stmt->bindParam(':target_amount', $data['target_amount']);
        $stmt->bindParam(':image_url', $data['image_url']);
        
        if ($stmt->execute()) {
            $project_id = $db->lastInsertId();
            
            echo json_encode([
                'success' => true,
                'message' => 'تم إنشاء المشروع بنجاح',
                'project_id' => $project_id
            ]);
        } else {
            throw new Exception('فشل في إنشاء المشروع');
        }
    } catch(PDOException $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في إنشاء المشروع: ' . $exception->getMessage()
        ]);
    }
}

function updateProject($db) {
    $data = json_decode(file_get_contents("php://input"), true);
    
    if (!$data || !isset($data['id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'معرف المشروع مطلوب'
        ]);
        return;
    }
    
    try {
        $query = "UPDATE projects SET 
                  title = :title,
                  description = :description,
                  category = :category,
                  target_amount = :target_amount,
                  status = :status,
                  image_url = :image_url
                  WHERE id = :id";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $data['id']);
        $stmt->bindParam(':title', $data['title']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':category', $data['category']);
        $stmt->bindParam(':target_amount', $data['target_amount']);
        $stmt->bindParam(':status', $data['status']);
        $stmt->bindParam(':image_url', $data['image_url']);
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث المشروع بنجاح'
            ]);
        } else {
            throw new Exception('فشل في تحديث المشروع');
        }
    } catch(PDOException $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في تحديث المشروع: ' . $exception->getMessage()
        ]);
    }
}

function deleteProject($db) {
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'معرف المشروع مطلوب'
        ]);
        return;
    }
    
    try {
        $query = "DELETE FROM projects WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id);
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'تم حذف المشروع بنجاح'
            ]);
        } else {
            throw new Exception('فشل في حذف المشروع');
        }
    } catch(PDOException $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في حذف المشروع: ' . $exception->getMessage()
        ]);
    }
}
?>
