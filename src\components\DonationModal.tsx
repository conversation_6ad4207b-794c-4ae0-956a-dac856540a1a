
import React, { useState } from 'react';
import { X, Upload, AlertCircle, CheckCircle, Heart } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface DonationModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: any;
}

const DonationModal = ({ isOpen, onClose, project }: DonationModalProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    fullName: '',
    country: '',
    whatsapp: '',
    email: '',
    amount: '',
    paymentProof: null as File | null
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  if (!isOpen || !project) return null;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (file: File) => {
    if (file && (file.type.includes('image') || file.type === 'application/pdf')) {
      setFormData(prev => ({ ...prev, paymentProof: file }));
    } else {
      toast({
        title: "خطأ في الملف",
        description: "يرجى رفع صورة أو ملف PDF فقط",
        variant: "destructive"
      });
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    const file = e.dataTransfer.files[0];
    if (file) handleFileChange(file);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validation
    if (!formData.fullName || !formData.country || !formData.whatsapp || !formData.email || !formData.amount) {
      toast({
        title: "يرجى ملء جميع الحقول",
        description: "جميع الحقول مطلوبة",
        variant: "destructive"
      });
      setIsSubmitting(false);
      return;
    }

    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "تم إرسال التبرع بنجاح! 🎉",
        description: "شكراً لك على تبرعك الكريم. سيتم مراجعة طلبك قريباً.",
      });

      // Reset form
      setFormData({
        fullName: '',
        country: '',
        whatsapp: '',
        email: '',
        amount: '',
        paymentProof: null
      });
      
      onClose();
    } catch (error) {
      toast({
        title: "حدث خطأ",
        description: "يرجى المحاولة مرة أخرى",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const countries = [
    "السعودية", "الإمارات", "الكويت", "قطر", "البحرين", "عمان",
    "الأردن", "لبنان", "سوريا", "العراق", "مصر", "المغرب",
    "الجزائر", "تونس", "ليبيا", "السودان", "اليمن", "فلسطين",
    "الولايات المتحدة", "كندا", "بريطانيا", "ألمانيا", "فرنسا", "أخرى"
  ];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto animate-scale-in">
        {/* Header */}
        <div className="relative bg-gradient-to-r from-gaza-green to-gaza-gold p-6 text-white rounded-t-2xl">
          <button
            onClick={onClose}
            className="absolute top-4 left-4 p-2 hover:bg-white/20 rounded-full transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
          
          <div className="text-center">
            <Heart className="h-12 w-12 mx-auto mb-3 text-gaza-gold" />
            <h2 className="text-2xl font-bold font-amiri mb-2">نموذج التبرع</h2>
            <p className="opacity-90">{project.title}</p>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Personal Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gaza-green border-b border-gaza-green/20 pb-2">
              المعلومات الشخصية
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">الاسم الكامل *</label>
                <input
                  type="text"
                  name="fullName"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gaza-green focus:border-transparent transition-all"
                  placeholder="اكتب اسمك الكامل"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">الدولة *</label>
                <select
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gaza-green focus:border-transparent transition-all"
                  required
                >
                  <option value="">اختر الدولة</option>
                  {countries.map(country => (
                    <option key={country} value={country}>{country}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">رقم الواتساب *</label>
                <input
                  type="tel"
                  name="whatsapp"
                  value={formData.whatsapp}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gaza-green focus:border-transparent transition-all"
                  placeholder="مثال: +966501234567"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">البريد الإلكتروني *</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gaza-green focus:border-transparent transition-all"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
            </div>
          </div>

          {/* Donation Amount */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gaza-green border-b border-gaza-green/20 pb-2">
              قيمة التبرع
            </h3>
            
            <div>
              <label className="block text-sm font-medium mb-2">المبلغ (ريال سعودي) *</label>
              <input
                type="number"
                name="amount"
                value={formData.amount}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gaza-green focus:border-transparent transition-all"
                placeholder="0.00"
                min="1"
                step="0.01"
                required
              />
            </div>

            {/* Quick Amount Buttons */}
            <div className="grid grid-cols-3 md:grid-cols-6 gap-2">
              {[50, 100, 250, 500, 1000, 2000].map(amount => (
                <button
                  key={amount}
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, amount: amount.toString() }))}
                  className="px-3 py-2 border border-gaza-green text-gaza-green rounded-lg hover:bg-gaza-green hover:text-white transition-all text-sm"
                >
                  {amount} ر.س
                </button>
              ))}
            </div>
          </div>

          {/* Payment Proof Upload */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gaza-green border-b border-gaza-green/20 pb-2">
              إثبات الدفع
            </h3>
            
            <div
              className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-all ${
                dragActive ? 'border-gaza-green bg-gaza-green/5' : 'border-gray-300'
              }`}
              onDrop={handleDrop}
              onDragOver={(e) => e.preventDefault()}
              onDragEnter={() => setDragActive(true)}
              onDragLeave={() => setDragActive(false)}
            >
              <input
                type="file"
                accept="image/*,.pdf"
                onChange={(e) => e.target.files?.[0] && handleFileChange(e.target.files[0])}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              
              <Upload className="h-12 w-12 mx-auto mb-4 text-gaza-green" />
              
              {formData.paymentProof ? (
                <div className="flex items-center justify-center gap-2 text-green-600">
                  <CheckCircle className="h-5 w-5" />
                  <span>{formData.paymentProof.name}</span>
                </div>
              ) : (
                <div>
                  <p className="text-lg font-medium mb-2">اسحب الملف هنا أو اضغط للتصفح</p>
                  <p className="text-sm text-muted-foreground">
                    يمكنك رفع صورة أو ملف PDF لإثبات الدفع
                  </p>
                </div>
              )}
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex gap-3">
                <AlertCircle className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                <div className="text-sm text-blue-700">
                  <p className="font-medium mb-1">طرق الدفع المتاحة:</p>
                  <ul className="space-y-1">
                    <li>• تحويل بنكي: البنك الأهلي - حساب رقم: 123456789</li>
                    <li>• محفظة إلكترونية: stc pay - 966501234567</li>
                    <li>• ويسترن يونيون: Gaza Support Foundation</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                جاري الإرسال...
              </>
            ) : (
              <>
                <Heart className="h-5 w-5" />
                إرسال التبرع
              </>
            )}
          </button>
        </form>
      </div>
    </div>
  );
};

export default DonationModal;
