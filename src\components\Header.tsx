
import React, { useState } from 'react';
import { Menu, X, <PERSON>, Settings } from 'lucide-react';
import { Link } from 'react-router-dom';
import DonationModal from './DonationModal';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const navigationItems = [
    { name: 'الرئيسية', href: '#home' },
    { name: 'المشاريع', href: '#projects' },
    { name: 'من نحن', href: '#about' },
    { name: 'تأثير التبرعات', href: '#impact' },
    { name: 'تواصل معنا', href: '#contact' },
  ];

  // مشروع عام للتبرع
  const generalProject = {
    id: 0,
    title: "تبرع عام لدعم أهل غزة",
    description: "تبرعك سيُستخدم في أكثر المشاريع احتياجاً لدعم أهل غزة",
    category: "عام"
  };

  const handleDonateClick = () => {
    setIsModalOpen(true);
    setIsMenuOpen(false);
  };

  return (
    <>
      <header className="fixed top-0 w-full bg-white/95 backdrop-blur-sm shadow-lg z-50 border-b border-gaza-gold/20">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="bg-gradient-to-r from-gaza-green to-gaza-gold p-2 rounded-lg">
                <Heart className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gaza-green font-amiri">دعم غزة</h1>
                <p className="text-xs text-muted-foreground">معاً نبني الأمل</p>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
              {navigationItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="text-foreground hover:text-gaza-green transition-colors duration-300 font-medium relative group"
                >
                  {item.name}
                  <span className="absolute -bottom-1 right-0 w-0 h-0.5 bg-gaza-gold transition-all duration-300 group-hover:w-full"></span>
                </a>
              ))}
            </nav>

            {/* Actions */}
            <div className="hidden md:flex items-center gap-3">
              <Link
                to="/admin"
                className="p-2 text-gaza-green hover:bg-gaza-green/10 rounded-lg transition-colors"
                title="لوحة الإدارة"
              >
                <Settings className="h-5 w-5" />
              </Link>
              <button onClick={handleDonateClick} className="btn-primary">
                تبرع الآن
              </button>
            </div>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-2 rounded-lg hover:bg-muted transition-colors"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden py-4 border-t border-gaza-gold/20 animate-fade-in">
              <nav className="flex flex-col space-y-4">
                {navigationItems.map((item) => (
                  <a
                    key={item.name}
                    href={item.href}
                    className="text-foreground hover:text-gaza-green transition-colors duration-300 font-medium py-2"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.name}
                  </a>
                ))}
                <Link
                  to="/admin"
                  className="text-gaza-green hover:text-gaza-green/80 transition-colors font-medium py-2 flex items-center gap-2"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Settings className="h-4 w-4" />
                  لوحة الإدارة
                </Link>
                <button onClick={handleDonateClick} className="btn-primary mt-4 w-full">
                  تبرع الآن
                </button>
              </nav>
            </div>
          )}
        </div>
      </header>

      {/* Donation Modal */}
      <DonationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        project={generalProject}
      />
    </>
  );
};

export default Header;
