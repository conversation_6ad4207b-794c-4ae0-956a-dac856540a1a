
import React, { useState } from 'react';
import { Users, Target, Calendar, ArrowLeft } from 'lucide-react';
import DonationModal from './DonationModal';

const Projects = () => {
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const projects = [
    {
      id: 1,
      title: "مساعدات طبية عاجلة",
      description: "توفير الأدوية والمعدات الطبية الضرورية للمستشفيات في غزة",
      image: "https://images.unsplash.com/photo-1584515933487-779824d29309?w=800&h=600&fit=crop",
      targetAmount: 100000,
      raisedAmount: 65000,
      donors: 1250,
      daysLeft: 15,
      category: "طبي",
      urgent: true
    },
    {
      id: 2,
      title: "مساعدات غذائية للأسر",
      description: "توزيع الطرود الغذائية على الأسر المحتاجة في قطاع غزة",
      image: "https://images.unsplash.com/photo-1593113598332-cd288d649433?w=800&h=600&fit=crop",
      targetAmount: 75000,
      raisedAmount: 45000,
      donors: 890,
      daysLeft: 20,
      category: "غذائي"
    },
    {
      id: 3,
      title: "تعليم الأطفال",
      description: "دعم التعليم وتوفير المستلزمات المدرسية للأطفال في غزة",
      image: "https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=800&h=600&fit=crop",
      targetAmount: 50000,
      raisedAmount: 30000,
      donors: 650,
      daysLeft: 30,
      category: "تعليمي"
    },
    {
      id: 4,
      title: "مشروع الإسكان الطارئ",
      description: "توفير المأوى المؤقت للأسر التي فقدت منازلها",
      image: "https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca?w=800&h=600&fit=crop",
      targetAmount: 200000,
      raisedAmount: 85000,
      donors: 920,
      daysLeft: 25,
      category: "إسكان",
      urgent: true
    },
    {
      id: 5,
      title: "مياه نظيفة للجميع",
      description: "حفر آبار وتوفير مياه شرب نظيفة للمناطق المحتاجة",
      image: "https://images.unsplash.com/photo-1541199249251-f713e6145474?w=800&h=600&fit=crop",
      targetAmount: 80000,
      raisedAmount: 52000,
      donors: 780,
      daysLeft: 18,
      category: "مياه"
    },
    {
      id: 6,
      title: "دعم الأرامل والأيتام",
      description: "رعاية شاملة للأرامل والأيتام وتوفير احتياجاتهم الأساسية",
      image: "https://images.unsplash.com/photo-1516627145497-ae4ef73a69e0?w=800&h=600&fit=crop",
      targetAmount: 120000,
      raisedAmount: 75000,
      donors: 1100,
      daysLeft: 22,
      category: "رعاية"
    }
  ];

  const handleDonateClick = (project: any) => {
    setSelectedProject(project);
    setIsModalOpen(true);
  };

  const getProgressPercentage = (raised: number, target: number) => {
    return Math.min((raised / target) * 100, 100);
  };

  return (
    <section id="projects" className="py-20 bg-gradient-to-b from-white to-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gaza-green font-amiri">
            مشاريعنا الحالية
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            اختر المشروع الذي تريد دعمه وكن جزءاً من التغيير الإيجابي في حياة الآلاف من الأسر في غزة
          </p>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <div
              key={project.id}
              className="bg-white rounded-2xl shadow-lg overflow-hidden card-hover animate-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* Project Image */}
              <div className="relative overflow-hidden h-48">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                />
                {project.urgent && (
                  <div className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    عاجل
                  </div>
                )}
                <div className="absolute top-4 left-4 bg-gaza-green text-white px-3 py-1 rounded-full text-sm font-semibold">
                  {project.category}
                </div>
              </div>

              {/* Project Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold mb-3 text-gaza-green font-amiri">
                  {project.title}
                </h3>
                <p className="text-muted-foreground mb-4 line-clamp-2">
                  {project.description}
                </p>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-semibold text-gaza-green">
                      {project.raisedAmount.toLocaleString()} ر.س
                    </span>
                    <span className="text-sm text-muted-foreground">
                      من {project.targetAmount.toLocaleString()} ر.س
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-gaza-green to-gaza-gold h-3 rounded-full transition-all duration-500"
                      style={{ width: `${getProgressPercentage(project.raisedAmount, project.targetAmount)}%` }}
                    ></div>
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {Math.round(getProgressPercentage(project.raisedAmount, project.targetAmount))}% مكتمل
                  </div>
                </div>

                {/* Project Stats */}
                <div className="flex items-center justify-between mb-6 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>{project.donors} متبرع</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>{project.daysLeft} يوم متبقي</span>
                  </div>
                </div>

                {/* Donate Button */}
                <button
                  onClick={() => handleDonateClick(project)}
                  className="w-full btn-primary flex items-center justify-center gap-2 group"
                >
                  تبرع الآن
                  <ArrowLeft className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        <div className="text-center mt-12">
          <button className="btn-secondary">
            عرض المزيد من المشاريع
          </button>
        </div>
      </div>

      {/* Donation Modal */}
      <DonationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        project={selectedProject}
      />
    </section>
  );
};

export default Projects;
