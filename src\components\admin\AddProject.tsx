
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ArrowRight, Upload, X } from 'lucide-react';
import { Input } from '../ui/input';
import { Label } from '../ui/label';

const AddProject = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    targetAmount: '',
    image_url: '',
    urgent: false,
    daysLeft: '',
    detailedDescription: ''
  });

  const categories = [
    'طبي',
    'غذائي',
    'تعليمي',
    'إسكان',
    'مياه',
    'ملابس',
    'كهرباء',
    'أخرى'
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.category || !formData.targetAmount || !formData.description) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    if (isNaN(Number(formData.targetAmount)) || Number(formData.targetAmount) <= 0) {
      alert('يرجى إدخال مبلغ صحيح');
      return;
    }

    setLoading(true);

    try {
      const projectData = {
        title: formData.title,
        description: formData.detailedDescription || formData.description,
        category: formData.category,
        target_amount: Number(formData.targetAmount),
        image_url: formData.image_url
      };

      const response = await fetch('http://localhost/gaza/api/projects.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(projectData)
      });

      const data = await response.json();

      if (data.success) {
        alert('تم إنشاء المشروع بنجاح!');
        navigate('/admin/projects');
      } else {
        alert(data.message || 'خطأ في إنشاء المشروع');
      }
    } catch (error) {
      alert('خطأ في الاتصال بالخادم');
      console.error('Error creating project:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link
          to="/admin/projects"
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowRight className="h-5 w-5" />
        </Link>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 font-amiri">إضافة مشروع جديد</h2>
          <p className="text-gray-600">قم بملء جميع البيانات المطلوبة لإضافة مشروع جديد</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 font-amiri">المعلومات الأساسية</h3>
            
            <div>
              <Label htmlFor="title">عنوان المشروع *</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="أدخل عنوان المشروع"
                required
              />
            </div>

            <div>
              <Label htmlFor="category">فئة المشروع *</Label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-gaza-green focus:border-transparent"
                required
              >
                <option value="">اختر الفئة</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="targetAmount">المبلغ المستهدف (ر.س) *</Label>
              <Input
                id="targetAmount"
                name="targetAmount"
                type="number"
                value={formData.targetAmount}
                onChange={handleInputChange}
                placeholder="0"
                required
              />
            </div>

            <div>
              <Label htmlFor="daysLeft">عدد الأيام المتبقية</Label>
              <Input
                id="daysLeft"
                name="daysLeft"
                type="number"
                value={formData.daysLeft}
                onChange={handleInputChange}
                placeholder="30"
              />
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="urgent"
                name="urgent"
                checked={formData.urgent}
                onChange={handleInputChange}
                className="rounded border-gray-300 text-gaza-green focus:ring-gaza-green"
              />
              <Label htmlFor="urgent">مشروع عاجل</Label>
            </div>
          </div>

          {/* Image URL */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 font-amiri">صورة المشروع</h3>

            <div>
              <Label htmlFor="image_url">رابط الصورة</Label>
              <Input
                id="image_url"
                name="image_url"
                type="url"
                value={formData.image_url}
                onChange={handleInputChange}
                placeholder="https://example.com/image.jpg"
              />
            </div>

            {formData.image_url && (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <p className="text-sm text-gray-600 mb-2">معاينة الصورة:</p>
                <img
                  src={formData.image_url}
                  alt="معاينة الصورة"
                  className="max-w-full h-48 object-cover rounded-lg mx-auto"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
              </div>
            )}
          </div>
        </div>

        {/* Description */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 font-amiri">تفاصيل المشروع</h3>
          
          <div>
            <Label htmlFor="description">وصف مختصر *</Label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              placeholder="وصف مختصر للمشروع (سيظهر في البطاقة)"
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-gaza-green focus:border-transparent resize-none"
              required
            />
          </div>

          <div>
            <Label htmlFor="detailedDescription">الوصف التفصيلي</Label>
            <textarea
              id="detailedDescription"
              name="detailedDescription"
              value={formData.detailedDescription}
              onChange={handleInputChange}
              rows={6}
              placeholder="وصف تفصيلي للمشروع وأهدافه وكيفية استخدام التبرعات"
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-gaza-green focus:border-transparent resize-none"
            />
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-4">
          <Link
            to="/admin/projects"
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            إلغاء
          </Link>
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-2 bg-gaza-green text-white rounded-lg hover:bg-gaza-green/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                جاري الحفظ...
              </>
            ) : (
              'إضافة المشروع'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddProject;
