
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ArrowRight, Upload, X } from 'lucide-react';
import { Input } from '../ui/input';
import { Label } from '../ui/label';

const AddProject = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    targetAmount: '',
    image: null as File | null,
    urgent: false,
    daysLeft: '',
    detailedDescription: ''
  });

  const categories = [
    'طبي',
    'غذائي',
    'تعليمي',
    'إسكان',
    'مياه',
    'رعاية',
    'عام'
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, image: file }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Project data:', formData);
    // هنا يمكن إضافة منطق حفظ المشروع
    navigate('/admin/projects');
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link
          to="/admin/projects"
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowRight className="h-5 w-5" />
        </Link>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 font-amiri">إضافة مشروع جديد</h2>
          <p className="text-gray-600">قم بملء جميع البيانات المطلوبة لإضافة مشروع جديد</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 font-amiri">المعلومات الأساسية</h3>
            
            <div>
              <Label htmlFor="title">عنوان المشروع *</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="أدخل عنوان المشروع"
                required
              />
            </div>

            <div>
              <Label htmlFor="category">فئة المشروع *</Label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-gaza-green focus:border-transparent"
                required
              >
                <option value="">اختر الفئة</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="targetAmount">المبلغ المستهدف (ر.س) *</Label>
              <Input
                id="targetAmount"
                name="targetAmount"
                type="number"
                value={formData.targetAmount}
                onChange={handleInputChange}
                placeholder="0"
                required
              />
            </div>

            <div>
              <Label htmlFor="daysLeft">عدد الأيام المتبقية</Label>
              <Input
                id="daysLeft"
                name="daysLeft"
                type="number"
                value={formData.daysLeft}
                onChange={handleInputChange}
                placeholder="30"
              />
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="urgent"
                name="urgent"
                checked={formData.urgent}
                onChange={handleInputChange}
                className="rounded border-gray-300 text-gaza-green focus:ring-gaza-green"
              />
              <Label htmlFor="urgent">مشروع عاجل</Label>
            </div>
          </div>

          {/* Image Upload */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 font-amiri">صورة المشروع</h3>
            
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              {formData.image ? (
                <div className="relative">
                  <img
                    src={URL.createObjectURL(formData.image)}
                    alt="Preview"
                    className="max-w-full h-48 object-cover rounded-lg mx-auto"
                  />
                  <button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, image: null }))}
                    className="absolute top-2 left-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ) : (
                <div>
                  <Upload className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600 mb-2">انقر لرفع صورة أو اسحبها هنا</p>
                  <p className="text-sm text-gray-400">PNG, JPG, GIF حتى 10MB</p>
                </div>
              )}
              <input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
            </div>
          </div>
        </div>

        {/* Description */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 font-amiri">تفاصيل المشروع</h3>
          
          <div>
            <Label htmlFor="description">وصف مختصر *</Label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              placeholder="وصف مختصر للمشروع (سيظهر في البطاقة)"
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-gaza-green focus:border-transparent resize-none"
              required
            />
          </div>

          <div>
            <Label htmlFor="detailedDescription">الوصف التفصيلي</Label>
            <textarea
              id="detailedDescription"
              name="detailedDescription"
              value={formData.detailedDescription}
              onChange={handleInputChange}
              rows={6}
              placeholder="وصف تفصيلي للمشروع وأهدافه وكيفية استخدام التبرعات"
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-gaza-green focus:border-transparent resize-none"
            />
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-4">
          <Link
            to="/admin/projects"
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            إلغاء
          </Link>
          <button
            type="submit"
            className="px-6 py-2 bg-gaza-green text-white rounded-lg hover:bg-gaza-green/90 transition-colors"
          >
            إضافة المشروع
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddProject;
