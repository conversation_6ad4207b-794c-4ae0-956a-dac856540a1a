
import React from 'react';
import { Users, Target, DollarSign, TrendingUp } from 'lucide-react';

const AdminDashboard = () => {
  const stats = [
    {
      title: 'إجمالي التبرعات',
      value: '2,450,000 ر.س',
      change: '+12%',
      icon: DollarSign,
      color: 'text-green-600'
    },
    {
      title: 'عدد المتبرعين',
      value: '5,847',
      change: '+8%',
      icon: Users,
      color: 'text-blue-600'
    },
    {
      title: 'المشاريع النشطة',
      value: '24',
      change: '+3',
      icon: Target,
      color: 'text-purple-600'
    },
    {
      title: 'معدل النمو',
      value: '18.2%',
      change: '+2.1%',
      icon: TrendingUp,
      color: 'text-orange-600'
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 font-amiri mb-2">
          مرحباً بك في لوحة الإدارة
        </h2>
        <p className="text-gray-600">
          نظرة عامة على أداء الموقع والتبرعات
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div
            key={index}
            className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">
                  {stat.title}
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {stat.value}
                </p>
                <p className={`text-sm ${stat.color} mt-1`}>
                  {stat.change} من الشهر الماضي
                </p>
              </div>
              <div className={`p-3 rounded-lg bg-gray-50`}>
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 font-amiri">
          النشاطات الأخيرة
        </h3>
        <div className="space-y-4">
          {[
            { action: 'تبرع جديد بقيمة 500 ر.س', time: 'منذ 5 دقائق', type: 'donation' },
            { action: 'إضافة مشروع جديد: مساعدات طبية', time: 'منذ ساعة', type: 'project' },
            { action: 'تحديث مشروع: مساعدات غذائية', time: 'منذ ساعتين', type: 'update' },
            { action: 'مستخدم جديد انضم للموقع', time: 'منذ 3 ساعات', type: 'user' }
          ].map((activity, index) => (
            <div key={index} className="flex items-center space-x-4 space-x-reverse py-3 border-b border-gray-100 last:border-b-0">
              <div className={`w-2 h-2 rounded-full ${
                activity.type === 'donation' ? 'bg-green-500' :
                activity.type === 'project' ? 'bg-blue-500' :
                activity.type === 'update' ? 'bg-yellow-500' : 'bg-purple-500'
              }`}></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                <p className="text-xs text-gray-500">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
