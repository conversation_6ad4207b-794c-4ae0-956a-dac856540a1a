
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Plus, Edit, Trash2, Eye, Search } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Input } from '../ui/input';

const AdminProjects = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const projects = [
    {
      id: 1,
      title: 'مساعدات طبية عاجلة',
      category: 'طبي',
      targetAmount: 100000,
      raisedAmount: 65000,
      status: 'نشط',
      createdAt: '2024-01-15'
    },
    {
      id: 2,
      title: 'مساعدات غذائية للأسر',
      category: 'غذائي',
      targetAmount: 75000,
      raisedAmount: 45000,
      status: 'نشط',
      createdAt: '2024-01-10'
    },
    {
      id: 3,
      title: 'تعليم الأطفال',
      category: 'تعليمي',
      targetAmount: 50000,
      raisedAmount: 30000,
      status: 'نشط',
      createdAt: '2024-01-05'
    },
    {
      id: 4,
      title: 'مشروع الإسكان الطارئ',
      category: 'إسكان',
      targetAmount: 200000,
      raisedAmount: 85000,
      status: 'متوقف',
      createdAt: '2024-01-01'
    }
  ];

  const filteredProjects = projects.filter(project =>
    project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    project.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'نشط':
        return 'bg-green-100 text-green-800';
      case 'متوقف':
        return 'bg-red-100 text-red-800';
      case 'مكتمل':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 font-amiri">إدارة المشاريع</h2>
          <p className="text-gray-600">إدارة جميع مشاريع التبرع</p>
        </div>
        <Link
          to="/admin/projects/add"
          className="bg-gaza-green text-white px-4 py-2 rounded-lg hover:bg-gaza-green/90 transition-colors flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          إضافة مشروع جديد
        </Link>
      </div>

      {/* Search */}
      <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-200">
        <div className="relative">
          <Search className="h-4 w-4 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="البحث في المشاريع..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pr-10"
          />
        </div>
      </div>

      {/* Projects Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-right">اسم المشروع</TableHead>
              <TableHead className="text-right">الفئة</TableHead>
              <TableHead className="text-right">الهدف</TableHead>
              <TableHead className="text-right">المُجمع</TableHead>
              <TableHead className="text-right">النسبة</TableHead>
              <TableHead className="text-right">الحالة</TableHead>
              <TableHead className="text-right">تاريخ الإنشاء</TableHead>
              <TableHead className="text-right">الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredProjects.map((project) => {
              const percentage = Math.round((project.raisedAmount / project.targetAmount) * 100);
              return (
                <TableRow key={project.id}>
                  <TableCell className="font-medium">{project.title}</TableCell>
                  <TableCell>
                    <span className="px-2 py-1 bg-gray-100 rounded-full text-xs">
                      {project.category}
                    </span>
                  </TableCell>
                  <TableCell>{project.targetAmount.toLocaleString()} ر.س</TableCell>
                  <TableCell>{project.raisedAmount.toLocaleString()} ر.س</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gaza-green h-2 rounded-full"
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                      <span className="text-xs">{percentage}%</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(project.status)}`}>
                      {project.status}
                    </span>
                  </TableCell>
                  <TableCell>{project.createdAt}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <button className="p-1 hover:bg-gray-100 rounded">
                        <Eye className="h-4 w-4 text-gray-600" />
                      </button>
                      <button className="p-1 hover:bg-gray-100 rounded">
                        <Edit className="h-4 w-4 text-blue-600" />
                      </button>
                      <button className="p-1 hover:bg-gray-100 rounded">
                        <Trash2 className="h-4 w-4 text-red-600" />
                      </button>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default AdminProjects;
