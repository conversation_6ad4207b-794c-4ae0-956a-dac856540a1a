
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Edit, Trash2, Eye, Search, RefreshCw } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Input } from '../ui/input';

interface Project {
  id: number;
  title: string;
  description?: string;
  category: string;
  target_amount: number;
  raised_amount: number;
  status: string;
  image_url?: string;
  created_at: string;
  updated_at: string;
  percentage: number;
}

const AdminProjects = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // جلب المشاريع من قاعدة البيانات
  const fetchProjects = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost/gaza/api/projects.php');
      const data = await response.json();

      if (data.success) {
        setProjects(data.data);
        setError(null);
      } else {
        setError(data.message || 'خطأ في جلب المشاريع');
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم');
      console.error('Error fetching projects:', err);
    } finally {
      setLoading(false);
    }
  };

  // حذف مشروع
  const deleteProject = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا المشروع؟')) {
      return;
    }

    try {
      const response = await fetch(`http://localhost/gaza/api/projects.php?id=${id}`, {
        method: 'DELETE'
      });
      const data = await response.json();

      if (data.success) {
        alert('تم حذف المشروع بنجاح');
        fetchProjects(); // إعادة جلب المشاريع
      } else {
        alert(data.message || 'خطأ في حذف المشروع');
      }
    } catch (err) {
      alert('خطأ في الاتصال بالخادم');
      console.error('Error deleting project:', err);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  const filteredProjects = projects.filter(project =>
    project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    project.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'نشط':
        return 'bg-green-100 text-green-800';
      case 'متوقف':
        return 'bg-red-100 text-red-800';
      case 'مكتمل':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-amiri">جاري تحميل المشاريع...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="text-red-600 ml-3">⚠️</div>
              <div>
                <h3 className="text-red-800 font-semibold">خطأ في تحميل المشاريع</h3>
                <p className="text-red-600">{error}</p>
              </div>
            </div>
            <button
              onClick={fetchProjects}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              إعادة المحاولة
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 font-amiri">إدارة المشاريع</h2>
          <p className="text-gray-600">إدارة جميع مشاريع التبرع ({projects.length} مشروع)</p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={fetchProjects}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            تحديث
          </button>
          <Link
            to="/admin/projects/add"
            className="bg-gaza-green text-white px-4 py-2 rounded-lg hover:bg-gaza-green/90 transition-colors flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            إضافة مشروع جديد
          </Link>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-200">
        <div className="relative">
          <Search className="h-4 w-4 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="البحث في المشاريع..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pr-10"
          />
        </div>
      </div>

      {/* Projects Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-right">الصورة</TableHead>
              <TableHead className="text-right">اسم المشروع</TableHead>
              <TableHead className="text-right">الفئة</TableHead>
              <TableHead className="text-right">الهدف</TableHead>
              <TableHead className="text-right">المُجمع</TableHead>
              <TableHead className="text-right">النسبة</TableHead>
              <TableHead className="text-right">الحالة</TableHead>
              <TableHead className="text-right">تاريخ الإنشاء</TableHead>
              <TableHead className="text-right">الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredProjects.map((project) => {
              const percentage = project.percentage || Math.round((project.raised_amount / project.target_amount) * 100);
              return (
                <TableRow key={project.id}>
                  <TableCell>
                    {project.image_url ? (
                      <img
                        src={`http://localhost/gaza/${project.image_url}`}
                        alt={project.title}
                        className="w-12 h-12 object-cover rounded-lg"
                        onError={(e) => {
                          e.currentTarget.src = 'https://via.placeholder.com/48x48?text=No+Image';
                        }}
                      />
                    ) : (
                      <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span className="text-xs text-gray-500">لا توجد</span>
                      </div>
                    )}
                  </TableCell>
                  <TableCell className="font-medium">{project.title}</TableCell>
                  <TableCell>
                    <span className="px-2 py-1 bg-gray-100 rounded-full text-xs">
                      {project.category}
                    </span>
                  </TableCell>
                  <TableCell>{Number(project.target_amount).toLocaleString()} ر.س</TableCell>
                  <TableCell>{Number(project.raised_amount).toLocaleString()} ر.س</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gaza-green h-2 rounded-full"
                          style={{ width: `${Math.min(percentage, 100)}%` }}
                        ></div>
                      </div>
                      <span className="text-xs">{percentage}%</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(project.status)}`}>
                      {project.status}
                    </span>
                  </TableCell>
                  <TableCell>{new Date(project.created_at).toLocaleDateString('ar-SA')}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <button
                        className="p-1 hover:bg-gray-100 rounded"
                        title="عرض التفاصيل"
                      >
                        <Eye className="h-4 w-4 text-gray-600" />
                      </button>
                      <button
                        className="p-1 hover:bg-gray-100 rounded"
                        title="تعديل المشروع"
                      >
                        <Edit className="h-4 w-4 text-blue-600" />
                      </button>
                      <button
                        className="p-1 hover:bg-gray-100 rounded"
                        title="حذف المشروع"
                        onClick={() => deleteProject(project.id)}
                      >
                        <Trash2 className="h-4 w-4 text-red-600" />
                      </button>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
            {filteredProjects.length === 0 && (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                  {searchTerm ? 'لا توجد مشاريع تطابق البحث' : 'لا توجد مشاريع'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default AdminProjects;
