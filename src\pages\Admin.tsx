
import React from 'react';
import { Routes, Route } from 'react-router-dom';
import AdminSidebar from '../components/admin/AdminSidebar';
import AdminDashboard from '../components/admin/AdminDashboard';
import AdminProjects from '../components/admin/AdminProjects';
import AddProject from '../components/admin/AddProject';

const Admin = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex rtl">
      <AdminSidebar />
      <div className="flex-1 flex flex-col">
        <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
          <h1 className="text-2xl font-bold text-gaza-green font-amiri">
            لوحة إدارة الموقع
          </h1>
        </header>
        <main className="flex-1 p-6">
          <Routes>
            <Route path="/" element={<AdminDashboard />} />
            <Route path="/projects" element={<AdminProjects />} />
            <Route path="/projects/add" element={<AddProject />} />
          </Routes>
        </main>
      </div>
    </div>
  );
};

export default Admin;
